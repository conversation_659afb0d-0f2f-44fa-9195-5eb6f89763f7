2025-08-07 02:55:46 - session_gui_integration - INFO - 开始刷新GUI界面，会话ID: 20250807_025546
2025-08-07 02:55:46 - session_gui_integration - INFO - 模型选项刷新完成，可用模型: 10 个
2025-08-07 02:55:46 - session_gui_integration - INFO - 可视化选项刷新完成，模型可用: True
2025-08-07 02:55:46 - session_gui_integration - INFO - GUI界面刷新完成
2025-08-07 02:55:46 - session_gui_integration - WARNING - 获取模型 DecisionTree 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:46 - session_gui_integration - WARNING - 获取模型 RandomForest 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:46 - session_gui_integration - WARNING - 获取模型 XGBoost 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 LightGBM 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 CatBoost 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 Logistic 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 SVM 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 KNN 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 NaiveBayes 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 NeuralNet 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
