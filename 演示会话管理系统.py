#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示训练会话管理系统的完整工作流程
展示如何创建会话、保存结果、恢复会话并在GUI中查看
"""

import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.pyplot as plt

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def demonstrate_session_workflow():
    """演示完整的会话管理工作流程"""
    print("🎯 训练会话管理系统演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 步骤1：创建新会话
    print("📁 步骤1：创建新的训练会话")
    print("-" * 30)
    
    try:
        from session_utils import create_new_session, get_active_session_id
        
        session_name = f"演示会话_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        description = "演示训练会话管理系统的完整功能"
        
        session_id = create_new_session(session_name, description, auto_activate=True)
        
        if session_id:
            print(f"✅ 会话创建成功")
            print(f"   会话ID: {session_id}")
            print(f"   会话名称: {session_name}")
            print(f"   当前活动会话: {get_active_session_id()}")
        else:
            print("❌ 会话创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建会话时出错: {e}")
        return False
    
    print()
    
    # 步骤2：模拟训练过程并保存结果
    print("🤖 步骤2：模拟模型训练并保存结果")
    print("-" * 30)
    
    try:
        from session_utils import save_to_session
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.linear_model import LogisticRegression
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, classification_report
        
        # 创建演示数据
        print("📊 生成演示数据集...")
        X, y = make_classification(
            n_samples=500, n_features=20, n_informative=15, 
            n_redundant=5, n_classes=2, random_state=42
        )
        
        feature_names = [f'特征_{i+1}' for i in range(X.shape[1])]
        X_df = pd.DataFrame(X, columns=feature_names)
        
        X_train, X_test, y_train, y_test = train_test_split(
            X_df, y, test_size=0.3, random_state=42
        )
        
        print(f"   训练集大小: {X_train.shape}")
        print(f"   测试集大小: {X_test.shape}")
        
        # 训练多个模型
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        model_results = {}
        
        for model_name, model in models.items():
            print(f"🔄 训练模型: {model_name}")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)
            
            # 计算性能
            accuracy = accuracy_score(y_test, y_pred)
            print(f"   准确率: {accuracy:.4f}")
            
            # 保存模型结果到会话
            model_data = {
                'model': model,
                'y_true': y_test,
                'y_pred': y_pred,
                'y_pred_proba': y_pred_proba,
                'X_test': X_test,
                'feature_names': feature_names,
                'accuracy': accuracy
            }
            
            model_path = save_to_session(
                model_data, 'model', model_name,
                model_type='single',
                additional_data={'accuracy': accuracy}
            )
            
            if model_path:
                print(f"   ✅ 模型已保存到会话")
                model_results[model_name] = accuracy
            else:
                print(f"   ❌ 模型保存失败")
            
            # 创建性能图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # 特征重要性图
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_
                indices = np.argsort(importances)[::-1][:10]  # 前10个重要特征
                
                ax1.bar(range(10), importances[indices])
                ax1.set_title(f'{model_name} - 特征重要性 (前10)')
                ax1.set_xlabel('特征排名')
                ax1.set_ylabel('重要性')
            
            # 预测分布图
            ax2.hist(y_pred_proba[:, 1], bins=20, alpha=0.7, edgecolor='black')
            ax2.set_title(f'{model_name} - 预测概率分布')
            ax2.set_xlabel('正类概率')
            ax2.set_ylabel('频次')
            
            plt.tight_layout()
            
            # 保存图表到会话
            plot_path = save_to_session(
                fig, 'plot', f'{model_name}_analysis',
                plot_type='single_model',
                model_name=model_name
            )
            
            if plot_path:
                print(f"   ✅ 图表已保存到会话")
            
            plt.close(fig)
        
        # 保存训练配置
        config_data = {
            'dataset_info': {
                'n_samples': X.shape[0],
                'n_features': X.shape[1],
                'n_classes': len(np.unique(y)),
                'test_size': 0.3
            },
            'models_trained': list(models.keys()),
            'training_time': datetime.now().isoformat(),
            'performance_summary': model_results
        }
        
        config_path = save_to_session(config_data, 'config', 'training_config')
        if config_path:
            print("✅ 训练配置已保存到会话")
        
    except Exception as e:
        print(f"❌ 模型训练过程出错: {e}")
        return False
    
    print()
    
    # 步骤3：显示会话信息
    print("📋 步骤3：查看会话信息")
    print("-" * 30)
    
    try:
        from session_utils import get_session_summary, list_all_sessions
        
        # 获取当前会话摘要
        summary = get_session_summary(session_id)
        if summary:
            print(f"📊 会话摘要:")
            print(f"   会话名称: {summary['basic_info']['session_name']}")
            print(f"   创建时间: {summary['basic_info']['created_time']}")
            print(f"   模型数量: {summary['statistics']['total_models']}")
            print(f"   图表数量: {summary['statistics']['total_plots']}")
            print(f"   状态: {summary['basic_info']['status']}")
            
            print(f"\n📈 训练的模型:")
            for model in summary['models']:
                status = "✅" if model['file_exists'] else "❌"
                print(f"   {status} {model['name']} ({model['type']})")
        
        # 显示所有会话
        sessions = list_all_sessions()
        print(f"\n📁 总共有 {len(sessions)} 个训练会话")
        
    except Exception as e:
        print(f"❌ 获取会话信息出错: {e}")
    
    print()
    
    # 步骤4：演示会话恢复
    print("🔄 步骤4：演示会话恢复功能")
    print("-" * 30)
    
    try:
        from session_utils import restore_session_to_cache
        
        print("💾 恢复会话到缓存...")
        success = restore_session_to_cache(session_id)
        
        if success:
            print("✅ 会话恢复成功")
            
            # 验证缓存中的模型
            from config import CACHE_PATH
            cache_files = list(CACHE_PATH.glob('*_results.joblib'))
            print(f"📂 缓存中现有 {len(cache_files)} 个模型文件")
            
            for cache_file in cache_files:
                model_name = cache_file.name.replace('_results.joblib', '')
                if model_name in model_results:
                    print(f"   ✅ {model_name} (准确率: {model_results[model_name]:.4f})")
        else:
            print("❌ 会话恢复失败")
            
    except Exception as e:
        print(f"❌ 会话恢复出错: {e}")
    
    print()
    
    # 步骤5：GUI使用指南
    print("🖥️  步骤5：GUI界面使用指南")
    print("-" * 30)
    
    print("现在您可以启动GUI应用程序来查看结果：")
    print()
    print("1️⃣  启动GUI:")
    print("   python gui_main.py")
    print()
    print("2️⃣  打开会话管理器:")
    print("   菜单栏 → 会话 → 会话管理器")
    print()
    print("3️⃣  选择刚创建的会话:")
    print(f"   会话名称: {session_name}")
    print(f"   会话ID: {session_id}")
    print()
    print("4️⃣  恢复会话（如果需要）:")
    print("   选择会话 → 点击'恢复到缓存'")
    print()
    print("5️⃣  查看模型结果:")
    print("   - 切换到'结果可视化'选项卡")
    print("   - 选择模型进行单模型可视化")
    print("   - 使用'模型比较'对比不同模型")
    print("   - 生成详细的性能报告")
    print()
    print("6️⃣  查看历史图表:")
    print(f"   图表保存在: training_sessions/{session_id}/plots/")
    print()
    
    # 显示会话目录结构
    print("📁 会话目录结构:")
    session_path = Path(f"training_sessions/{session_id}")
    if session_path.exists():
        for item in session_path.rglob('*'):
            if item.is_file():
                relative_path = item.relative_to(session_path)
                print(f"   📄 {relative_path}")
    
    print()
    print("🎉 演示完成！")
    print("=" * 60)
    print("✨ 训练会话管理系统已成功演示所有功能")
    print("💡 现在您可以使用GUI界面查看和分析所有训练结果")
    
    return True


if __name__ == "__main__":
    try:
        success = demonstrate_session_workflow()
        if success:
            print("\n🚀 演示成功完成！请启动GUI查看结果。")
        else:
            print("\n❌ 演示过程中出现错误。")
        
    except KeyboardInterrupt:
        print("\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生意外错误: {e}")
        import traceback
        traceback.print_exc()
