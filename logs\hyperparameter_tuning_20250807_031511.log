2025-08-07 03:15:11 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 03:15:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 03:15:11 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9818
2025-08-07 03:15:12 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9833
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 72, 'max_depth': 20, 'min_samples_split': 8, 'min_samples_leaf': 10, 'max_features': 'sqrt'}
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 03:15:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_031516.html
2025-08-07 03:15:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_031516.html
2025-08-07 03:15:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.62 秒
