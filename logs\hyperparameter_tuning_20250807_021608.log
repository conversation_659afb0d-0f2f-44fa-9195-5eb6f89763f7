2025-08-07 02:16:08 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9434
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9492
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9492
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 10, 'min_samples_split': 22, 'min_samples_leaf': 12, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9492
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_021609.html
2025-08-07 02:16:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_021609.html
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.98 秒
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9810
2025-08-07 02:16:14 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9825
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 82, 'max_depth': 16, 'min_samples_split': 10, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_021619.html
2025-08-07 02:16:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_021620.html
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.48 秒
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9844
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 52, 'max_depth': 10, 'learning_rate': 0.295068367288394, 'subsample': 0.5074020368153942, 'colsample_bytree': 0.9123332263997124}
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9869
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:23 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_021623.html
2025-08-07 02:16:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_021624.html
2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.06 秒
2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:25 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9837
2025-08-07 02:16:26 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9853
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9861
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 133, 'max_depth': 10, 'learning_rate': 0.054988376829664065, 'feature_fraction': 0.6586467386569148, 'bagging_fraction': 0.8266155061399189}
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9861
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:27 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_021627.html
2025-08-07 02:16:27 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_021627.html
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.49 秒
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:30 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9788
2025-08-07 02:16:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:35 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:40 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:43 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:55 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:59 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:07 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9799
2025-08-07 02:17:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:16 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:21 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:26 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9812
2025-08-07 02:17:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:35 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:37 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:48 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 130, 'depth': 3, 'learning_rate': 0.10843367565734939, 'l2_leaf_reg': 9.898610837129109, 'bagging_temperature': 0.16091271923750772}
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9812
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/50
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250807_021752.html
2025-08-07 02:17:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250807_021753.html
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 85.63 秒
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9648
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9672
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 4.984458089054056, 'solver': 'liblinear'}
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_021753.html
2025-08-07 02:17:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_021754.html
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.77 秒
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9601
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9876
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9895
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9919
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 9.835073932081382, 'kernel': 'rbf'}
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_021755.html
2025-08-07 02:17:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_021755.html
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.39 秒
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9753
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_021755.html
2025-08-07 02:17:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_021756.html
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.80 秒
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:57 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9815
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (50, 50), 'alpha': 0.005733123343606008}
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9815
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:18:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:18:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_021809.html
2025-08-07 02:18:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:18:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_021810.html
2025-08-07 02:18:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.83 秒
