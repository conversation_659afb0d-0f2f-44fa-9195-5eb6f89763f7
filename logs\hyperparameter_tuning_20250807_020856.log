2025-08-07 02:08:56 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 20
2025-08-07 02:09:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 162, 'max_depth': 25, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-08-07 02:09:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9730
2025-08-07 02:09:12 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:09:12 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_020912.html
2025-08-07 02:09:12 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:09:12 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_020912.html
2025-08-07 02:09:12 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 15.41 秒
2025-08-07 02:09:12 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 02:09:12 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 5，最小改善: 0.01
2025-08-07 02:09:13 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9601
2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 早停触发：连续 5 轮没有显著改善（阈值: 0.01）
2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9648
2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 148, 'max_depth': 16, 'min_samples_split': 10, 'min_samples_leaf': 3, 'max_features': 'log2'}
2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9648
2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 6/50
2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:09:17 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:09:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_020917.html
2025-08-07 02:09:18 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:09:18 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_020918.html
2025-08-07 02:09:18 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 5.80 秒
2025-08-07 02:09:18 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 02:09:18 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 15，最小改善: 0.001
2025-08-07 02:09:19 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9225
2025-08-07 02:09:19 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9593
2025-08-07 02:09:28 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9656
2025-08-07 02:09:37 - hyperparameter_tuning - INFO - Trial 30: 发现更好的得分 0.9722
2025-08-07 02:09:53 - hyperparameter_tuning - INFO - 早停触发：连续 15 轮没有显著改善（阈值: 0.001）
2025-08-07 02:09:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-07 02:09:53 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 280, 'max_depth': 21, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-07 02:09:53 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9726
2025-08-07 02:09:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 46/50
2025-08-07 02:09:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:09:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:09:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_020953.html
2025-08-07 02:09:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:09:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_020954.html
2025-08-07 02:09:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 36.15 秒
