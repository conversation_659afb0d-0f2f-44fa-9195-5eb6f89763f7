2025-08-07 03:22:43 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 03:22:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 03:22:44 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9794
2025-08-07 03:22:45 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9818
2025-08-07 03:22:46 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9842
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9851
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 56, 'max_depth': 25, 'min_samples_split': 7, 'min_samples_leaf': 9, 'max_features': 'log2'}
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9851
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 03:22:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_032250.html
2025-08-07 03:22:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_032250.html
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 7.16 秒
