#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import os
import numpy as np
from pathlib import Path
from joblib import dump
from sklearn.metrics import accuracy_score, roc_auc_score, confusion_matrix, classification_report
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from catboost import CatBoostClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from config import CACHE_PATH, MODEL_DISPLAY_NAMES, RANDOM_SEED
from logger import get_logger

# 尝试导入会话管理功能
try:
    from session_utils import get_current_session, save_to_session
except ImportError:
    # 如果无法导入会话管理，提供空函数
    def get_current_session():
        return None

    def save_to_session(data, data_type, name, **kwargs):
        return None

logger = get_logger(__name__)

# 确保缓存目录存在
CACHE_PATH.mkdir(parents=True, exist_ok=True)


class ModelTrainer:
    """
    统一的模型训练器类，消除重复代码
    """

    def __init__(self, model_name, model_class, default_params=None):
        """
        初始化模型训练器

        Args:
            model_name: 模型名称
            model_class: 模型类
            default_params: 默认参数
        """
        self.model_name = model_name
        self.model_class = model_class
        self.default_params = default_params or {}

    def train_and_evaluate(self, X_train, y_train, X_test, y_test, params=None):
        """
        训练和评估模型的统一方法

        Args:
            X_train, y_train: 训练数据
            X_test, y_test: 测试数据
            params: 自定义参数

        Returns:
            训练好的模型
        """
        start_time = time.time()

        # 合并参数
        final_params = self.default_params.copy()
        if params:
            final_params.update(params)

        # 创建模型
        model = self.model_class(**final_params)

        # 训练模型
        model.fit(X_train, y_train)

        # 预测
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else model.predict(X_test)

        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba) if hasattr(model, 'predict_proba') else roc_auc_score(y_test, y_pred)

        # 输出结果
        display_name = MODEL_DISPLAY_NAMES.get(self.model_name, self.model_name)
        logger.info(f"模型名称: {display_name}")
        logger.info(f"准确率: {accuracy:.4f}")
        logger.info(f"AUC: {auc:.4f}")
        logger.info("混淆矩阵:")
        logger.info(f"\n{confusion_matrix(y_test, y_pred)}")
        logger.info("\n分类报告:")
        logger.info(f"\n{classification_report(y_test, y_pred)}")

        end_time = time.time()
        logger.info(f"训练时间: {end_time - start_time:.2f} 秒")

        # 缓存结果
        self._cache_results(model, y_test, y_pred, y_pred_proba, X_test)

        return model

    def _cache_results(self, model, y_true, y_pred, y_pred_proba, X_test):
        """缓存模型结果"""
        # 确保所有数据都不为None
        if y_true is None or y_pred is None or X_test is None:
            logger.error(f"模型 {self.model_name} 的关键数据为None，无法保存")
            return

        # 构建完整的结果数据
        result = {
            'model': model,
            'y_true': y_true,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'X_test': X_test
        }

        # 添加特征名称到结果中
        if hasattr(X_test, 'columns'):
            result['feature_names'] = list(X_test.columns)
        else:
            # 如果没有列名，创建默认特征名称
            n_features = X_test.shape[1] if hasattr(X_test, 'shape') else len(X_test[0])
            result['feature_names'] = [f'feature_{i}' for i in range(n_features)]

        # 添加性能指标
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            metrics = {
                'accuracy': accuracy_score(y_true, y_pred),
                'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
            }

            # 如果是二分类，添加AUC
            if y_pred_proba is not None and len(set(y_true)) == 2:
                from sklearn.metrics import roc_auc_score
                try:
                    metrics['auc'] = roc_auc_score(y_true, y_pred_proba[:, 1])
                except:
                    metrics['auc'] = None

            result['metrics'] = metrics
            logger.info(f"模型 {self.model_name} 性能: 准确率={metrics['accuracy']:.4f}")

        except Exception as e:
            logger.warning(f"计算性能指标失败: {e}")
            result['metrics'] = {}

        # 优先使用会话管理系统保存
        current_session = get_current_session()
        if current_session:
            try:
                session_path = save_to_session(
                    result, 'model', self.model_name,
                    model_type='single',
                    additional_data={
                        'training_time': getattr(self, '_training_time', None),
                        'model_params': getattr(model, 'get_params', lambda: {})(),
                        'data_shape': X_test.shape if hasattr(X_test, 'shape') else None,
                        'n_classes': len(set(y_true))
                    }
                )

                if session_path:
                    logger.info(f"模型 {self.model_name} 已保存到会话: {session_path}")

                    # 同时保存到传统缓存以保持兼容性
                    cache_file = CACHE_PATH / f"{self.model_name}_results.joblib"
                    dump(result, cache_file)
                    logger.info(f"模型 {self.model_name} 同时保存到缓存: {cache_file}")

                    return
            except Exception as e:
                logger.warning(f"保存到会话失败，使用传统方式: {e}")

        # 传统缓存方式作为备选
        cache_file = CACHE_PATH / f"{self.model_name}_results.joblib"
        dump(result, cache_file)
        logger.info(f"模型 {self.model_name} 的结果已缓存到: {cache_file}")

        # 保存特征名称（如果X_test是DataFrame）
        try:
            feature_names_file = CACHE_PATH / f"{self.model_name}_feature_names.joblib"
            dump(result['feature_names'], feature_names_file)
            logger.info(f"特征名称已缓存到: {feature_names_file}")
        except Exception as e:
            logger.warning(f"保存特征名称失败: {e}")


# 模型训练器实例
MODEL_TRAINERS = {
    'DecisionTree': ModelTrainer('DecisionTree', DecisionTreeClassifier, {
        'random_state': RANDOM_SEED,
        'max_depth': 5,  # 限制深度防止过拟合
        'min_samples_split': 20,  # 增加分割所需最小样本数
        'min_samples_leaf': 10,   # 增加叶节点最小样本数
        'class_weight': 'balanced'  # 处理不平衡数据
    }),
    'RandomForest': ModelTrainer('RandomForest', RandomForestClassifier, {'random_state': RANDOM_SEED}),
    'XGBoost': ModelTrainer('XGBoost', XGBClassifier, {'random_state': RANDOM_SEED, 'use_label_encoder': False, 'eval_metric': 'logloss'}),
    'LightGBM': ModelTrainer('LightGBM', LGBMClassifier, {'random_state': RANDOM_SEED}),
    'CatBoost': ModelTrainer('CatBoost', CatBoostClassifier, {'random_state': RANDOM_SEED, 'verbose': 0}),
    'Logistic': ModelTrainer('Logistic', LogisticRegression, {'random_state': RANDOM_SEED, 'max_iter': 2000}),
    'SVM': ModelTrainer('SVM', SVC, {'random_state': RANDOM_SEED, 'probability': True}),
    'KNN': ModelTrainer('KNN', KNeighborsClassifier, {}),
    'NaiveBayes': ModelTrainer('NaiveBayes', GaussianNB, {}),
    'NeuralNet': ModelTrainer('NeuralNet', MLPClassifier, {'random_state': RANDOM_SEED, 'max_iter': 2000})
}


# 保持向后兼容的训练函数（使用新的ModelTrainer类）
def train_decision_tree(X_train, y_train, X_test, y_test, params=None):
    """训练决策树模型"""
    return MODEL_TRAINERS['DecisionTree'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_random_forest(X_train, y_train, X_test, y_test, params=None):
    """训练随机森林模型"""
    return MODEL_TRAINERS['RandomForest'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_xgboost(X_train, y_train, X_test, y_test, params=None):
    """训练XGBoost模型"""
    return MODEL_TRAINERS['XGBoost'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_lightgbm(X_train, y_train, X_test, y_test, params=None):
    """训练LightGBM模型"""
    return MODEL_TRAINERS['LightGBM'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_catboost(X_train, y_train, X_test, y_test, params=None):
    """训练CatBoost模型"""
    return MODEL_TRAINERS['CatBoost'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_logistic(X_train, y_train, X_test, y_test, params=None):
    """训练逻辑回归模型"""
    return MODEL_TRAINERS['Logistic'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_svm(X_train, y_train, X_test, y_test, params=None):
    """训练SVM模型"""
    return MODEL_TRAINERS['SVM'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_knn(X_train, y_train, X_test, y_test, params=None):
    """训练KNN模型"""
    return MODEL_TRAINERS['KNN'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_naive_bayes(X_train, y_train, X_test, y_test, params=None):
    """训练朴素贝叶斯模型"""
    return MODEL_TRAINERS['NaiveBayes'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_neural_net(X_train, y_train, X_test, y_test, params=None):
    """训练神经网络模型"""
    return MODEL_TRAINERS['NeuralNet'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


# 兼容旧版本的train_and_evaluate函数
def train_and_evaluate(model, model_name, X_train, y_train, X_test, y_test):
    """
    兼容旧版本的训练和评估函数
    建议使用新的ModelTrainer类
    """
    logger.warning("使用了已弃用的train_and_evaluate函数，建议使用ModelTrainer类")

    start_time = time.time()
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else model.predict(X_test)

    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_pred_proba) if hasattr(model, 'predict_proba') else roc_auc_score(y_test, y_pred)

    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    logger.info(f"模型名称: {display_name}")
    logger.info(f"准确率: {accuracy:.4f}")
    logger.info(f"AUC: {auc:.4f}")
    logger.info("混淆矩阵:")
    logger.info(f"\n{confusion_matrix(y_test, y_pred)}")
    logger.info("\n分类报告:")
    logger.info(f"\n{classification_report(y_test, y_pred)}")

    end_time = time.time()
    logger.info(f"训练时间: {end_time - start_time:.2f} 秒")

    # 缓存模型结果
    result = {
        'model': model,
        'y_true': y_test,
        'y_pred': y_pred,
        'y_pred_proba': y_pred_proba if hasattr(model, 'predict_proba') else y_pred,
        'X_test': X_test
    }
    cache_file = CACHE_PATH / f"{model_name}_results.joblib"
    dump(result, cache_file)
    logger.info(f"模型 {model_name} 的结果已缓存到: {cache_file}")

    # 保存特征名称（如果X_test是DataFrame）
    try:
        if hasattr(X_test, 'columns'):
            feature_names = list(X_test.columns)
            feature_names_file = CACHE_PATH / f"{model_name}_feature_names.joblib"
            dump(feature_names, feature_names_file)
            logger.info(f"特征名称已缓存到: {feature_names_file}")
    except Exception as e:
        logger.warning(f"保存特征名称失败: {e}")

    return model
