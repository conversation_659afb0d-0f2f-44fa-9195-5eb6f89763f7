2025-07-15 20:06:18 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-07-15 20:06:18 - best_model_selector - INFO - 加载模型训练结果...
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-07-15 20:06:18 - best_model_selector - INFO - 计算综合性能指标...
2025-07-15 20:06:18 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-07-15 20:06:18 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: XGBoost (得分: 0.9735)
2025-07-15 20:06:18 - best_model_selector - INFO - 模型选择结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型推荐结果
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-07-15 20:06:18 - best_model_selector - INFO - 推荐策略: balanced
2025-07-15 20:06:18 - best_model_selector - INFO - 最佳模型: XGBoost
2025-07-15 20:06:18 - best_model_selector - INFO - 综合得分: 0.9735
2025-07-15 20:06:18 - best_model_selector - INFO - 
前三名模型:
2025-07-15 20:06:18 - best_model_selector - INFO -   1. XGBoost: 0.9735
2025-07-15 20:06:18 - best_model_selector - INFO -   2. Random Forest: 0.9692
2025-07-15 20:06:18 - best_model_selector - INFO -   3. LightGBM: 0.9690
2025-07-15 20:06:18 - best_model_selector - INFO -   4. KNN: 0.8641
2025-07-15 20:06:18 - best_model_selector - INFO -   5. Naive Bayes: 0.8464
2025-07-15 20:06:18 - best_model_selector - INFO - 
推荐 XGBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.9735
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9879
   - F1分数: 0.9754
   - MCC: 0.9504

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-07-15 20:06:18 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:13 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:14:13 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:14:13 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:14:13 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-08-07 02:14:13 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8848)
2025-08-07 02:14:13 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:14:13 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:13 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:14:13 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:13 - best_model_selector - INFO - 推荐策略: balanced
2025-08-07 02:14:13 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:14:13 - best_model_selector - INFO - 综合得分: 0.8848
2025-08-07 02:14:13 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:14:13 - best_model_selector - INFO -   1. CatBoost: 0.8848
2025-08-07 02:14:13 - best_model_selector - INFO -   2. Random Forest: 0.8796
2025-08-07 02:14:13 - best_model_selector - INFO -   3. XGBoost: 0.8611
2025-08-07 02:14:13 - best_model_selector - INFO -   4. Neural Network: 0.8592
2025-08-07 02:14:13 - best_model_selector - INFO -   5. LightGBM: 0.8560
2025-08-07 02:14:13 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.8848
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9591
   - F1分数: 0.8750
   - MCC: 0.7965

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:14:13 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:28 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:14:28 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:14:28 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:14:28 - best_model_selector - INFO - 使用 interpretability 策略计算综合得分...
2025-08-07 02:14:28 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: RandomForest (得分: 0.8752)
2025-08-07 02:14:28 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:14:28 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:28 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:14:28 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:28 - best_model_selector - INFO - 推荐策略: interpretability
2025-08-07 02:14:28 - best_model_selector - INFO - 最佳模型: Random Forest
2025-08-07 02:14:28 - best_model_selector - INFO - 综合得分: 0.8752
2025-08-07 02:14:28 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:14:28 - best_model_selector - INFO -   1. Random Forest: 0.8752
2025-08-07 02:14:28 - best_model_selector - INFO -   2. Logistic Regression: 0.8669
2025-08-07 02:14:28 - best_model_selector - INFO -   3. CatBoost: 0.8543
2025-08-07 02:14:28 - best_model_selector - INFO -   4. Decision Tree: 0.8540
2025-08-07 02:14:28 - best_model_selector - INFO -   5. XGBoost: 0.8416
2025-08-07 02:14:28 - best_model_selector - INFO - 
推荐 Random Forest 作为最佳模型的理由：

1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752
2. 可解释性评分: 0.80，模型决策过程相对透明
3. 在保持可解释性的同时，F1分数达到 0.8750

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:14:28 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:24 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:24 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:24 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:24 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-08-07 02:20:24 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8848)
2025-08-07 02:20:24 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:24 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:24 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:24 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:24 - best_model_selector - INFO - 推荐策略: balanced
2025-08-07 02:20:24 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:20:24 - best_model_selector - INFO - 综合得分: 0.8848
2025-08-07 02:20:24 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:24 - best_model_selector - INFO -   1. CatBoost: 0.8848
2025-08-07 02:20:24 - best_model_selector - INFO -   2. Random Forest: 0.8796
2025-08-07 02:20:24 - best_model_selector - INFO -   3. XGBoost: 0.8611
2025-08-07 02:20:24 - best_model_selector - INFO -   4. Neural Network: 0.8592
2025-08-07 02:20:24 - best_model_selector - INFO -   5. LightGBM: 0.8560
2025-08-07 02:20:24 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.8848
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9591
   - F1分数: 0.8750
   - MCC: 0.7965

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:24 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:44 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:44 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:44 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:44 - best_model_selector - INFO - 使用 performance 策略计算综合得分...
2025-08-07 02:20:44 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.9061)
2025-08-07 02:20:44 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:44 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:44 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:44 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:44 - best_model_selector - INFO - 推荐策略: performance
2025-08-07 02:20:44 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:20:44 - best_model_selector - INFO - 综合得分: 0.9061
2025-08-07 02:20:44 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:44 - best_model_selector - INFO -   1. CatBoost: 0.9061
2025-08-07 02:20:44 - best_model_selector - INFO -   2. Random Forest: 0.9000
2025-08-07 02:20:44 - best_model_selector - INFO -   3. XGBoost: 0.8827
2025-08-07 02:20:44 - best_model_selector - INFO -   4. Neural Network: 0.8804
2025-08-07 02:20:44 - best_model_selector - INFO -   5. LightGBM: 0.8766
2025-08-07 02:20:44 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在性能优先策略下，该模型获得了最高的综合得分 0.9061
2. AUC-ROC: 0.9591，表现出色的分类能力
3. F1分数: 0.8750，在精确率和召回率之间取得良好平衡

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:44 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:48 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:48 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:48 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:48 - best_model_selector - INFO - 使用 robustness 策略计算综合得分...
2025-08-07 02:20:48 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8471)
2025-08-07 02:20:48 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:48 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:48 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:48 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:48 - best_model_selector - INFO - 推荐策略: robustness
2025-08-07 02:20:48 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:20:48 - best_model_selector - INFO - 综合得分: 0.8471
2025-08-07 02:20:48 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:48 - best_model_selector - INFO -   1. CatBoost: 0.8471
2025-08-07 02:20:48 - best_model_selector - INFO -   2. Random Forest: 0.8450
2025-08-07 02:20:48 - best_model_selector - INFO -   3. XGBoost: 0.8124
2025-08-07 02:20:48 - best_model_selector - INFO -   4. Neural Network: 0.8117
2025-08-07 02:20:48 - best_model_selector - INFO -   5. LightGBM: 0.8104
2025-08-07 02:20:48 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在稳健性优先策略下，该模型获得了最高的综合得分 0.8471
2. 马修斯相关系数(MCC): 0.7965，显示出良好的稳健性
3. 平衡准确率: 0.8900，在不平衡数据上表现稳定

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:48 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:55 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:55 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:55 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:55 - best_model_selector - INFO - 使用 interpretability 策略计算综合得分...
2025-08-07 02:20:55 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: RandomForest (得分: 0.8752)
2025-08-07 02:20:55 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:55 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:55 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:55 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:55 - best_model_selector - INFO - 推荐策略: interpretability
2025-08-07 02:20:55 - best_model_selector - INFO - 最佳模型: Random Forest
2025-08-07 02:20:55 - best_model_selector - INFO - 综合得分: 0.8752
2025-08-07 02:20:55 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:55 - best_model_selector - INFO -   1. Random Forest: 0.8752
2025-08-07 02:20:55 - best_model_selector - INFO -   2. Logistic Regression: 0.8669
2025-08-07 02:20:55 - best_model_selector - INFO -   3. CatBoost: 0.8543
2025-08-07 02:20:55 - best_model_selector - INFO -   4. Decision Tree: 0.8540
2025-08-07 02:20:55 - best_model_selector - INFO -   5. XGBoost: 0.8416
2025-08-07 02:20:55 - best_model_selector - INFO - 
推荐 Random Forest 作为最佳模型的理由：

1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752
2. 可解释性评分: 0.80，模型决策过程相对透明
3. 在保持可解释性的同时，F1分数达到 0.8750

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:55 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:15 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:15 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:15 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:15 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-08-07 02:44:15 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8848)
2025-08-07 02:44:15 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:15 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:15 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:15 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:15 - best_model_selector - INFO - 推荐策略: balanced
2025-08-07 02:44:15 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:44:15 - best_model_selector - INFO - 综合得分: 0.8848
2025-08-07 02:44:15 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:15 - best_model_selector - INFO -   1. CatBoost: 0.8848
2025-08-07 02:44:15 - best_model_selector - INFO -   2. Random Forest: 0.8796
2025-08-07 02:44:15 - best_model_selector - INFO -   3. XGBoost: 0.8611
2025-08-07 02:44:15 - best_model_selector - INFO -   4. Neural Network: 0.8592
2025-08-07 02:44:15 - best_model_selector - INFO -   5. LightGBM: 0.8560
2025-08-07 02:44:15 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.8848
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9591
   - F1分数: 0.8750
   - MCC: 0.7965

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:15 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:19 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:19 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:19 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:19 - best_model_selector - INFO - 使用 performance 策略计算综合得分...
2025-08-07 02:44:19 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.9061)
2025-08-07 02:44:19 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:19 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:19 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:19 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:19 - best_model_selector - INFO - 推荐策略: performance
2025-08-07 02:44:19 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:44:19 - best_model_selector - INFO - 综合得分: 0.9061
2025-08-07 02:44:19 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:19 - best_model_selector - INFO -   1. CatBoost: 0.9061
2025-08-07 02:44:19 - best_model_selector - INFO -   2. Random Forest: 0.9000
2025-08-07 02:44:19 - best_model_selector - INFO -   3. XGBoost: 0.8827
2025-08-07 02:44:19 - best_model_selector - INFO -   4. Neural Network: 0.8804
2025-08-07 02:44:19 - best_model_selector - INFO -   5. LightGBM: 0.8766
2025-08-07 02:44:19 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在性能优先策略下，该模型获得了最高的综合得分 0.9061
2. AUC-ROC: 0.9591，表现出色的分类能力
3. F1分数: 0.8750，在精确率和召回率之间取得良好平衡

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:19 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:23 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:23 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:23 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:23 - best_model_selector - INFO - 使用 robustness 策略计算综合得分...
2025-08-07 02:44:23 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8471)
2025-08-07 02:44:23 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:23 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:23 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:23 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:23 - best_model_selector - INFO - 推荐策略: robustness
2025-08-07 02:44:23 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:44:23 - best_model_selector - INFO - 综合得分: 0.8471
2025-08-07 02:44:23 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:23 - best_model_selector - INFO -   1. CatBoost: 0.8471
2025-08-07 02:44:23 - best_model_selector - INFO -   2. Random Forest: 0.8450
2025-08-07 02:44:23 - best_model_selector - INFO -   3. XGBoost: 0.8124
2025-08-07 02:44:23 - best_model_selector - INFO -   4. Neural Network: 0.8117
2025-08-07 02:44:23 - best_model_selector - INFO -   5. LightGBM: 0.8104
2025-08-07 02:44:23 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在稳健性优先策略下，该模型获得了最高的综合得分 0.8471
2. 马修斯相关系数(MCC): 0.7965，显示出良好的稳健性
3. 平衡准确率: 0.8900，在不平衡数据上表现稳定

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:23 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:26 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:26 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:26 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:26 - best_model_selector - INFO - 使用 interpretability 策略计算综合得分...
2025-08-07 02:44:26 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: RandomForest (得分: 0.8752)
2025-08-07 02:44:26 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:26 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:26 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:26 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:26 - best_model_selector - INFO - 推荐策略: interpretability
2025-08-07 02:44:26 - best_model_selector - INFO - 最佳模型: Random Forest
2025-08-07 02:44:26 - best_model_selector - INFO - 综合得分: 0.8752
2025-08-07 02:44:26 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:26 - best_model_selector - INFO -   1. Random Forest: 0.8752
2025-08-07 02:44:26 - best_model_selector - INFO -   2. Logistic Regression: 0.8669
2025-08-07 02:44:26 - best_model_selector - INFO -   3. CatBoost: 0.8543
2025-08-07 02:44:26 - best_model_selector - INFO -   4. Decision Tree: 0.8540
2025-08-07 02:44:26 - best_model_selector - INFO -   5. XGBoost: 0.8416
2025-08-07 02:44:26 - best_model_selector - INFO - 
推荐 Random Forest 作为最佳模型的理由：

1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752
2. 可解释性评分: 0.80，模型决策过程相对透明
3. 在保持可解释性的同时，F1分数达到 0.8750

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:26 - best_model_selector - INFO - ============================================================
