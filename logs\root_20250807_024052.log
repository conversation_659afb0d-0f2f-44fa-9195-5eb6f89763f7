2025-08-07 02:40:53 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-07 02:40:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:40:54 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 02:40:54 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:40:54 - GUI - INFO - GUI界面初始化完成
2025-08-07 02:41:49 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 02:41:49 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9476
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9508
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9510
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 11, 'min_samples_leaf': 9, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9510
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:41:49 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:41:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_024149.html
2025-08-07 02:41:49 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:41:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_024149.html
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.87 秒
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:41:50 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9786
2025-08-07 02:41:51 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9825
2025-08-07 02:41:56 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9867
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9867
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 52, 'max_depth': 12, 'min_samples_split': 20, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9867
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:41:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:41:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_024159.html
2025-08-07 02:41:59 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:41:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_024159.html
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.94 秒
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9844
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9860
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9877
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9877
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 285, 'max_depth': 5, 'learning_rate': 0.09204322552303883, 'subsample': 0.6033292458419579, 'colsample_bytree': 0.5844739004508576}
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9877
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:05 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_024205.html
2025-08-07 02:42:05 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_024205.html
2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.08 秒
2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9829
2025-08-07 02:42:07 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9845
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 152, 'max_depth': 2, 'learning_rate': 0.16790990806242068, 'feature_fraction': 0.5090878949690316, 'bagging_fraction': 0.8412312178348227}
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9845
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:08 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_024208.html
2025-08-07 02:42:08 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_024208.html
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.99 秒
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9792
2025-08-07 02:42:10 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:15 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:19 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:25 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:39 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:40 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9800
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 147, 'depth': 2, 'learning_rate': 0.29283569955820826, 'l2_leaf_reg': 7.365856343711115, 'bagging_temperature': 0.6167328973724523}
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9800
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:51 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250807_024251.html
2025-08-07 02:42:51 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250807_024251.html
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 42.85 秒
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9672
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 4.924538599914636, 'solver': 'lbfgs'}
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_024252.html
2025-08-07 02:42:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_024252.html
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.76 秒
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9593
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9624
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9876
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9886
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9911
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 9.028803611806707, 'kernel': 'rbf'}
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/50
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_024253.html
2025-08-07 02:42:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_024253.html
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.19 秒
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9712
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9744
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_024254.html
2025-08-07 02:42:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:42:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_024254.html
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.98 秒
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:55 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9806
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (100,), 'alpha': 0.0021985870869764604}
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9815
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:43:07 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:43:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:43:08 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_024307.html
2025-08-07 02:43:08 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:43:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:43:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_024308.html
2025-08-07 02:43:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.60 秒
2025-08-07 02:43:17 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317
2025-08-07 02:43:17 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250807_024317 (ID: 20250807_024317)
2025-08-07 02:43:17 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250807_024317
2025-08-07 02:43:17 - session_utils - INFO - 创建新会话: 训练_nodule2_20250807_024317 (ID: 20250807_024317)
2025-08-07 02:43:17 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 02:43:17 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 02:43:17 - model_training - INFO - 模型名称: Decision Tree
2025-08-07 02:43:17 - model_training - INFO - 准确率: 0.8000
2025-08-07 02:43:17 - model_training - INFO - AUC: 0.8951
2025-08-07 02:43:17 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:17 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-07 02:43:17 - model_training - INFO - 
分类报告:
2025-08-07 02:43:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-07 02:43:17 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:43:17 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\DecisionTree_single_024317.joblib
2025-08-07 02:43:17 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\DecisionTree_single_024317.joblib
2025-08-07 02:43:18 - model_training - INFO - 模型名称: Random Forest
2025-08-07 02:43:18 - model_training - INFO - 准确率: 0.9000
2025-08-07 02:43:18 - model_training - INFO - AUC: 0.9386
2025-08-07 02:43:18 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:18 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 02:43:18 - model_training - INFO - 
分类报告:
2025-08-07 02:43:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 02:43:18 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 02:43:18 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\RandomForest_single_024318.joblib
2025-08-07 02:43:18 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\RandomForest_single_024318.joblib
2025-08-07 02:43:18 - model_training - INFO - 模型名称: XGBoost
2025-08-07 02:43:18 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:43:18 - model_training - INFO - AUC: 0.9668
2025-08-07 02:43:18 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:18 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:43:18 - model_training - INFO - 
分类报告:
2025-08-07 02:43:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:43:18 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 02:43:18 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\XGBoost_single_024318.joblib
2025-08-07 02:43:18 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\XGBoost_single_024318.joblib
2025-08-07 02:43:18 - model_training - INFO - 模型名称: LightGBM
2025-08-07 02:43:18 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:43:18 - model_training - INFO - AUC: 0.9463
2025-08-07 02:43:18 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:18 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:43:18 - model_training - INFO - 
分类报告:
2025-08-07 02:43:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:43:18 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 02:43:18 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\LightGBM_single_024318.joblib
2025-08-07 02:43:18 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\LightGBM_single_024318.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型名称: CatBoost
2025-08-07 02:43:19 - model_training - INFO - 准确率: 0.9000
2025-08-07 02:43:19 - model_training - INFO - AUC: 0.9591
2025-08-07 02:43:19 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:19 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 02:43:19 - model_training - INFO - 
分类报告:
2025-08-07 02:43:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 02:43:19 - model_training - INFO - 训练时间: 1.01 秒
2025-08-07 02:43:19 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\CatBoost_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\CatBoost_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型名称: Logistic Regression
2025-08-07 02:43:19 - model_training - INFO - 准确率: 0.8500
2025-08-07 02:43:19 - model_training - INFO - AUC: 0.9284
2025-08-07 02:43:19 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:19 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-07 02:43:19 - model_training - INFO - 
分类报告:
2025-08-07 02:43:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-07 02:43:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:43:19 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\Logistic_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\Logistic_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型名称: SVM
2025-08-07 02:43:19 - model_training - INFO - 准确率: 0.8000
2025-08-07 02:43:19 - model_training - INFO - AUC: 0.9207
2025-08-07 02:43:19 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:19 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-08-07 02:43:19 - model_training - INFO - 
分类报告:
2025-08-07 02:43:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-08-07 02:43:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:43:19 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\SVM_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\SVM_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型名称: KNN
2025-08-07 02:43:19 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:43:19 - model_training - INFO - AUC: 0.9322
2025-08-07 02:43:19 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:19 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:43:19 - model_training - INFO - 
分类报告:
2025-08-07 02:43:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:43:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:43:19 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\KNN_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\KNN_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型名称: Naive Bayes
2025-08-07 02:43:19 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:43:19 - model_training - INFO - AUC: 0.8977
2025-08-07 02:43:19 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:19 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-07 02:43:19 - model_training - INFO - 
分类报告:
2025-08-07 02:43:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:43:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:43:19 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\NaiveBayes_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\NaiveBayes_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型名称: Neural Network
2025-08-07 02:43:19 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:43:19 - model_training - INFO - AUC: 0.9591
2025-08-07 02:43:19 - model_training - INFO - 混淆矩阵:
2025-08-07 02:43:19 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:43:19 - model_training - INFO - 
分类报告:
2025-08-07 02:43:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:43:19 - model_training - INFO - 训练时间: 0.24 秒
2025-08-07 02:43:19 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\NeuralNet_single_024319.joblib
2025-08-07 02:43:19 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_024317\models\NeuralNet_single_024319.joblib
2025-08-07 02:44:15 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:15 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:15 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:15 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:15 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-08-07 02:44:15 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8848)
2025-08-07 02:44:15 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:15 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:15 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:15 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:15 - best_model_selector - INFO - 推荐策略: balanced
2025-08-07 02:44:15 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:44:15 - best_model_selector - INFO - 综合得分: 0.8848
2025-08-07 02:44:15 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:15 - best_model_selector - INFO -   1. CatBoost: 0.8848
2025-08-07 02:44:15 - best_model_selector - INFO -   2. Random Forest: 0.8796
2025-08-07 02:44:15 - best_model_selector - INFO -   3. XGBoost: 0.8611
2025-08-07 02:44:15 - best_model_selector - INFO -   4. Neural Network: 0.8592
2025-08-07 02:44:15 - best_model_selector - INFO -   5. LightGBM: 0.8560
2025-08-07 02:44:15 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.8848
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9591
   - F1分数: 0.8750
   - MCC: 0.7965

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:15 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:19 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:19 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:19 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:19 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:19 - best_model_selector - INFO - 使用 performance 策略计算综合得分...
2025-08-07 02:44:19 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.9061)
2025-08-07 02:44:19 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:19 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:19 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:19 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:19 - best_model_selector - INFO - 推荐策略: performance
2025-08-07 02:44:19 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:44:19 - best_model_selector - INFO - 综合得分: 0.9061
2025-08-07 02:44:19 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:19 - best_model_selector - INFO -   1. CatBoost: 0.9061
2025-08-07 02:44:19 - best_model_selector - INFO -   2. Random Forest: 0.9000
2025-08-07 02:44:19 - best_model_selector - INFO -   3. XGBoost: 0.8827
2025-08-07 02:44:19 - best_model_selector - INFO -   4. Neural Network: 0.8804
2025-08-07 02:44:19 - best_model_selector - INFO -   5. LightGBM: 0.8766
2025-08-07 02:44:19 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在性能优先策略下，该模型获得了最高的综合得分 0.9061
2. AUC-ROC: 0.9591，表现出色的分类能力
3. F1分数: 0.8750，在精确率和召回率之间取得良好平衡

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:19 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:23 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:23 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:23 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:23 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:23 - best_model_selector - INFO - 使用 robustness 策略计算综合得分...
2025-08-07 02:44:23 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8471)
2025-08-07 02:44:23 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:23 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:23 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:23 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:23 - best_model_selector - INFO - 推荐策略: robustness
2025-08-07 02:44:23 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:44:23 - best_model_selector - INFO - 综合得分: 0.8471
2025-08-07 02:44:23 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:23 - best_model_selector - INFO -   1. CatBoost: 0.8471
2025-08-07 02:44:23 - best_model_selector - INFO -   2. Random Forest: 0.8450
2025-08-07 02:44:23 - best_model_selector - INFO -   3. XGBoost: 0.8124
2025-08-07 02:44:23 - best_model_selector - INFO -   4. Neural Network: 0.8117
2025-08-07 02:44:23 - best_model_selector - INFO -   5. LightGBM: 0.8104
2025-08-07 02:44:23 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在稳健性优先策略下，该模型获得了最高的综合得分 0.8471
2. 马修斯相关系数(MCC): 0.7965，显示出良好的稳健性
3. 平衡准确率: 0.8900，在不平衡数据上表现稳定

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:23 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:26 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:44:26 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:44:26 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:44:26 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:44:26 - best_model_selector - INFO - 使用 interpretability 策略计算综合得分...
2025-08-07 02:44:26 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: RandomForest (得分: 0.8752)
2025-08-07 02:44:26 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:44:26 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:26 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:44:26 - best_model_selector - INFO - ============================================================
2025-08-07 02:44:26 - best_model_selector - INFO - 推荐策略: interpretability
2025-08-07 02:44:26 - best_model_selector - INFO - 最佳模型: Random Forest
2025-08-07 02:44:26 - best_model_selector - INFO - 综合得分: 0.8752
2025-08-07 02:44:26 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:44:26 - best_model_selector - INFO -   1. Random Forest: 0.8752
2025-08-07 02:44:26 - best_model_selector - INFO -   2. Logistic Regression: 0.8669
2025-08-07 02:44:26 - best_model_selector - INFO -   3. CatBoost: 0.8543
2025-08-07 02:44:26 - best_model_selector - INFO -   4. Decision Tree: 0.8540
2025-08-07 02:44:26 - best_model_selector - INFO -   5. XGBoost: 0.8416
2025-08-07 02:44:26 - best_model_selector - INFO - 
推荐 Random Forest 作为最佳模型的理由：

1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752
2. 可解释性评分: 0.80，模型决策过程相对透明
3. 在保持可解释性的同时，F1分数达到 0.8750

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:44:26 - best_model_selector - INFO - ============================================================
