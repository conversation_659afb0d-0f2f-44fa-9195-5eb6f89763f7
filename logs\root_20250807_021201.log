2025-08-07 02:12:03 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:12:03 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 02:12:03 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:12:03 - GUI - INFO - GUI界面初始化完成
2025-08-07 02:12:42 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 02:12:42 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 02:12:42 - model_training - INFO - 模型名称: Decision Tree
2025-08-07 02:12:42 - model_training - INFO - 准确率: 0.8000
2025-08-07 02:12:42 - model_training - INFO - AUC: 0.8951
2025-08-07 02:12:42 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:42 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-07 02:12:42 - model_training - INFO - 
分类报告:
2025-08-07 02:12:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-07 02:12:42 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:12:42 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-07 02:12:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-08-07 02:12:42 - model_training - INFO - 模型名称: Random Forest
2025-08-07 02:12:42 - model_training - INFO - 准确率: 0.9000
2025-08-07 02:12:42 - model_training - INFO - AUC: 0.9386
2025-08-07 02:12:42 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:42 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 02:12:42 - model_training - INFO - 
分类报告:
2025-08-07 02:12:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 02:12:42 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 02:12:42 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-07 02:12:42 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-07 02:12:42 - model_training - INFO - 模型名称: XGBoost
2025-08-07 02:12:42 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:12:42 - model_training - INFO - AUC: 0.9668
2025-08-07 02:12:42 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:42 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:12:42 - model_training - INFO - 
分类报告:
2025-08-07 02:12:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:12:42 - model_training - INFO - 训练时间: 0.05 秒
2025-08-07 02:12:43 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-07 02:12:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-07 02:12:44 - model_training - INFO - 模型名称: LightGBM
2025-08-07 02:12:44 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:12:44 - model_training - INFO - AUC: 0.9463
2025-08-07 02:12:44 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:44 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:12:44 - model_training - INFO - 
分类报告:
2025-08-07 02:12:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:12:44 - model_training - INFO - 训练时间: 1.38 秒
2025-08-07 02:12:44 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-07 02:12:44 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-07 02:12:45 - model_training - INFO - 模型名称: CatBoost
2025-08-07 02:12:45 - model_training - INFO - 准确率: 0.9000
2025-08-07 02:12:45 - model_training - INFO - AUC: 0.9591
2025-08-07 02:12:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:45 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 02:12:45 - model_training - INFO - 
分类报告:
2025-08-07 02:12:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 02:12:45 - model_training - INFO - 训练时间: 1.18 秒
2025-08-07 02:12:45 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-07 02:12:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-08-07 02:12:45 - model_training - INFO - 模型名称: Logistic Regression
2025-08-07 02:12:45 - model_training - INFO - 准确率: 0.8500
2025-08-07 02:12:45 - model_training - INFO - AUC: 0.9284
2025-08-07 02:12:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:45 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-07 02:12:45 - model_training - INFO - 
分类报告:
2025-08-07 02:12:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-07 02:12:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:12:45 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-07 02:12:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-07 02:12:45 - model_training - INFO - 模型名称: SVM
2025-08-07 02:12:45 - model_training - INFO - 准确率: 0.8000
2025-08-07 02:12:45 - model_training - INFO - AUC: 0.9207
2025-08-07 02:12:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:45 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-08-07 02:12:45 - model_training - INFO - 
分类报告:
2025-08-07 02:12:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-08-07 02:12:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:12:45 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-07 02:12:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-08-07 02:12:45 - model_training - INFO - 模型名称: KNN
2025-08-07 02:12:45 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:12:45 - model_training - INFO - AUC: 0.9322
2025-08-07 02:12:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:45 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:12:45 - model_training - INFO - 
分类报告:
2025-08-07 02:12:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:12:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:12:45 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-07 02:12:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-08-07 02:12:45 - model_training - INFO - 模型名称: Naive Bayes
2025-08-07 02:12:45 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:12:45 - model_training - INFO - AUC: 0.8977
2025-08-07 02:12:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:45 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-07 02:12:45 - model_training - INFO - 
分类报告:
2025-08-07 02:12:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:12:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:12:45 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-07 02:12:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-08-07 02:12:46 - model_training - INFO - 模型名称: Neural Network
2025-08-07 02:12:46 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:12:46 - model_training - INFO - AUC: 0.9591
2025-08-07 02:12:46 - model_training - INFO - 混淆矩阵:
2025-08-07 02:12:46 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:12:46 - model_training - INFO - 
分类报告:
2025-08-07 02:12:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:12:46 - model_training - INFO - 训练时间: 0.36 秒
2025-08-07 02:12:46 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-07 02:12:46 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-08-07 02:14:13 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:14:13 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:14:13 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:14:13 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:14:13 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-08-07 02:14:13 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8848)
2025-08-07 02:14:13 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:14:13 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:13 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:14:13 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:13 - best_model_selector - INFO - 推荐策略: balanced
2025-08-07 02:14:13 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:14:13 - best_model_selector - INFO - 综合得分: 0.8848
2025-08-07 02:14:13 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:14:13 - best_model_selector - INFO -   1. CatBoost: 0.8848
2025-08-07 02:14:13 - best_model_selector - INFO -   2. Random Forest: 0.8796
2025-08-07 02:14:13 - best_model_selector - INFO -   3. XGBoost: 0.8611
2025-08-07 02:14:13 - best_model_selector - INFO -   4. Neural Network: 0.8592
2025-08-07 02:14:13 - best_model_selector - INFO -   5. LightGBM: 0.8560
2025-08-07 02:14:13 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.8848
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9591
   - F1分数: 0.8750
   - MCC: 0.7965

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:14:13 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:28 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:14:28 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:14:28 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:14:28 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:14:28 - best_model_selector - INFO - 使用 interpretability 策略计算综合得分...
2025-08-07 02:14:28 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: RandomForest (得分: 0.8752)
2025-08-07 02:14:28 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:14:28 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:28 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:14:28 - best_model_selector - INFO - ============================================================
2025-08-07 02:14:28 - best_model_selector - INFO - 推荐策略: interpretability
2025-08-07 02:14:28 - best_model_selector - INFO - 最佳模型: Random Forest
2025-08-07 02:14:28 - best_model_selector - INFO - 综合得分: 0.8752
2025-08-07 02:14:28 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:14:28 - best_model_selector - INFO -   1. Random Forest: 0.8752
2025-08-07 02:14:28 - best_model_selector - INFO -   2. Logistic Regression: 0.8669
2025-08-07 02:14:28 - best_model_selector - INFO -   3. CatBoost: 0.8543
2025-08-07 02:14:28 - best_model_selector - INFO -   4. Decision Tree: 0.8540
2025-08-07 02:14:28 - best_model_selector - INFO -   5. XGBoost: 0.8416
2025-08-07 02:14:28 - best_model_selector - INFO - 
推荐 Random Forest 作为最佳模型的理由：

1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752
2. 可解释性评分: 0.80，模型决策过程相对透明
3. 在保持可解释性的同时，F1分数达到 0.8750

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:14:28 - best_model_selector - INFO - ============================================================
