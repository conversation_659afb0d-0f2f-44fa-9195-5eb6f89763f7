2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9476
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9508
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9510
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 11, 'min_samples_leaf': 9, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9510
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:41:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_024149.html
2025-08-07 02:41:49 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_024149.html
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.87 秒
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 02:41:49 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:41:50 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9786
2025-08-07 02:41:51 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9825
2025-08-07 02:41:56 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9867
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9867
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 52, 'max_depth': 12, 'min_samples_split': 20, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9867
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:41:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_024159.html
2025-08-07 02:41:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_024159.html
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.94 秒
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:41:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9844
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9860
2025-08-07 02:42:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9877
2025-08-07 02:42:02 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:03 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:04 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9877
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 285, 'max_depth': 5, 'learning_rate': 0.09204322552303883, 'subsample': 0.6033292458419579, 'colsample_bytree': 0.5844739004508576}
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9877
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:05 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_024205.html
2025-08-07 02:42:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_024205.html
2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 6.08 秒
2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 02:42:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9829
2025-08-07 02:42:07 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9845
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 152, 'max_depth': 2, 'learning_rate': 0.16790990806242068, 'feature_fraction': 0.5090878949690316, 'bagging_fraction': 0.8412312178348227}
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9845
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:08 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_024208.html
2025-08-07 02:42:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_024208.html
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.99 秒
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:09 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9792
2025-08-07 02:42:10 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:15 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:19 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:25 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:39 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:40 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9800
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 147, 'depth': 2, 'learning_rate': 0.29283569955820826, 'l2_leaf_reg': 7.365856343711115, 'bagging_temperature': 0.6167328973724523}
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9800
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250807_024251.html
2025-08-07 02:42:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250807_024251.html
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 42.85 秒
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:51 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9672
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 4.924538599914636, 'solver': 'lbfgs'}
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_024252.html
2025-08-07 02:42:52 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_024252.html
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.76 秒
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9593
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9624
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9876
2025-08-07 02:42:52 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9886
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9911
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 9.028803611806707, 'kernel': 'rbf'}
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/50
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_024253.html
2025-08-07 02:42:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_024253.html
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.19 秒
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9712
2025-08-07 02:42:53 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9744
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 实际执行试验次数: 13/50
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:42:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_024254.html
2025-08-07 02:42:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_024254.html
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.98 秒
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 02:42:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:42:55 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9806
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (100,), 'alpha': 0.0021985870869764604}
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9815
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:43:07 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:43:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:43:08 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_024307.html
2025-08-07 02:43:08 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:43:08 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_024308.html
2025-08-07 02:43:08 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.60 秒
