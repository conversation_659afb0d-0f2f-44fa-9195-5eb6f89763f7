2025-08-07 02:55:45 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-07 02:55:46 - training_session_manager - INFO - 创建会话目录结构: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_025546
2025-08-07 02:55:46 - training_session_manager - INFO - 创建训练会话: GUI测试会话_20250807_025546 (ID: 20250807_025546)
2025-08-07 02:55:46 - training_session_manager - INFO - 创建新会话: GUI测试会话_20250807_025546
2025-08-07 02:55:46 - session_utils - INFO - 创建新会话: GUI测试会话_20250807_025546 (ID: 20250807_025546)
2025-08-07 02:55:46 - training_session_manager - INFO - 保存模型 RandomForest 到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_025546\models\RandomForest_single_025546.joblib
2025-08-07 02:55:46 - training_session_manager - INFO - 保存图片 RandomForest_performance 到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_025546\plots\single_model\RandomForest_RandomForest_performance_025546.png
2025-08-07 02:55:46 - training_session_manager - INFO - 保存模型 Logistic 到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_025546\models\Logistic_single_025546.joblib
2025-08-07 02:55:46 - training_session_manager - INFO - 保存图片 Logistic_performance 到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_025546\plots\single_model\Logistic_Logistic_performance_025546.png
2025-08-07 02:55:46 - training_session_manager - INFO - 成功加载会话: GUI测试会话_20250807_025546
2025-08-07 02:55:46 - session_loader - INFO - 初始化会话加载器
2025-08-07 02:55:46 - session_loader - INFO - 成功加载模型: RandomForest
2025-08-07 02:55:46 - session_loader - INFO - 成功加载模型: Logistic
2025-08-07 02:55:46 - session_loader - INFO - 恢复模型缓存: RandomForest
2025-08-07 02:55:46 - session_loader - INFO - 恢复模型缓存: Logistic
2025-08-07 02:55:46 - session_loader - INFO - 成功恢复会话 GUI测试会话_20250807_025546 到缓存目录
2025-08-07 02:55:46 - session_gui_integration - INFO - 开始刷新GUI界面，会话ID: 20250807_025546
2025-08-07 02:55:46 - session_gui_integration - INFO - 模型选项刷新完成，可用模型: 10 个
2025-08-07 02:55:46 - session_gui_integration - INFO - 可视化选项刷新完成，模型可用: True
2025-08-07 02:55:46 - training_session_manager - INFO - 成功加载会话: GUI测试会话_20250807_025546
2025-08-07 02:55:46 - session_loader - INFO - 初始化会话加载器
2025-08-07 02:55:46 - training_session_manager - INFO - 成功加载会话: GUI测试会话_20250807_025546
2025-08-07 02:55:46 - session_loader - INFO - 初始化会话加载器
2025-08-07 02:55:46 - session_gui_integration - INFO - GUI界面刷新完成
2025-08-07 02:55:46 - session_gui_integration - WARNING - 获取模型 DecisionTree 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:46 - session_gui_integration - WARNING - 获取模型 RandomForest 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:46 - session_gui_integration - WARNING - 获取模型 XGBoost 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 LightGBM 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 CatBoost 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 Logistic 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 SVM 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 KNN 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 NaiveBayes 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
2025-08-07 02:55:47 - session_gui_integration - WARNING - 获取模型 NeuralNet 性能失败: The 'y_true' parameter of accuracy_score must be an array-like or a sparse matrix. Got None instead.
