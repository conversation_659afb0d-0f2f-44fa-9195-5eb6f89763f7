2025-08-07 03:22:26 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-07 03:22:26 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:22:27 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 03:22:27 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 03:22:27 - GUI - INFO - GUI界面初始化完成
2025-08-07 03:22:43 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 03:22:43 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 03:22:43 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 03:22:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 03:22:44 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9794
2025-08-07 03:22:45 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9818
2025-08-07 03:22:46 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9842
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9851
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 56, 'max_depth': 25, 'min_samples_split': 7, 'min_samples_leaf': 9, 'max_features': 'log2'}
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9851
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 03:22:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 03:22:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_032250.html
2025-08-07 03:22:50 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 03:22:50 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_032250.html
2025-08-07 03:22:50 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 7.16 秒
2025-08-07 03:22:53 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_032253
2025-08-07 03:22:53 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250807_032253 (ID: 20250807_032253)
2025-08-07 03:22:53 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250807_032253
2025-08-07 03:22:53 - session_utils - INFO - 创建新会话: 训练_nodule2_20250807_032253 (ID: 20250807_032253)
2025-08-07 03:22:53 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 03:22:53 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 03:22:53 - model_training - INFO - 模型名称: Random Forest
2025-08-07 03:22:53 - model_training - INFO - 准确率: 0.9000
2025-08-07 03:22:53 - model_training - INFO - AUC: 0.9386
2025-08-07 03:22:53 - model_training - INFO - 混淆矩阵:
2025-08-07 03:22:53 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 03:22:53 - model_training - INFO - 
分类报告:
2025-08-07 03:22:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 03:22:53 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 03:22:53 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-07 03:22:53 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_032253\models\RandomForest_single_032253.joblib
2025-08-07 03:22:53 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_032253\models\RandomForest_single_032253.joblib
2025-08-07 03:22:53 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
