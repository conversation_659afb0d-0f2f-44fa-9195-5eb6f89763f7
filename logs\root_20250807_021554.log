2025-08-07 02:15:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:15:56 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 02:15:56 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:15:56 - GUI - INFO - GUI界面初始化完成
2025-08-07 02:16:08 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 02:16:08 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9434
2025-08-07 02:16:08 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9492
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9492
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 10, 'min_samples_split': 22, 'min_samples_leaf': 12, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9492
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_021609.html
2025-08-07 02:16:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_021609.html
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.98 秒
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 02:16:09 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9810
2025-08-07 02:16:14 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9825
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 82, 'max_depth': 16, 'min_samples_split': 10, 'min_samples_leaf': 8, 'max_features': 'sqrt'}
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:19 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_021619.html
2025-08-07 02:16:20 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:20 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_021620.html
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.48 秒
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9844
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:20 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-07 02:16:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 52, 'max_depth': 10, 'learning_rate': 0.295068367288394, 'subsample': 0.5074020368153942, 'colsample_bytree': 0.9123332263997124}
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9869
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:23 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:23 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:23 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_021623.html
2025-08-07 02:16:24 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_021624.html
2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.06 秒
2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 02:16:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:25 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9837
2025-08-07 02:16:26 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9853
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9861
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 133, 'max_depth': 10, 'learning_rate': 0.054988376829664065, 'feature_fraction': 0.6586467386569148, 'bagging_fraction': 0.8266155061399189}
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9861
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:16:27 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:27 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_021627.html
2025-08-07 02:16:27 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:16:27 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_021627.html
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.49 秒
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:16:27 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:30 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9788
2025-08-07 02:16:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:35 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:40 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:43 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:55 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:16:59 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:07 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9799
2025-08-07 02:17:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:16 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:21 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:26 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9812
2025-08-07 02:17:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:30 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:35 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:37 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:47 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:48 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 130, 'depth': 3, 'learning_rate': 0.10843367565734939, 'l2_leaf_reg': 9.898610837129109, 'bagging_temperature': 0.16091271923750772}
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9812
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/50
2025-08-07 02:17:52 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:52 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250807_021752.html
2025-08-07 02:17:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250807_021753.html
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 85.63 秒
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9648
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9672
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 4.984458089054056, 'solver': 'liblinear'}
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:53 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:53 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_021753.html
2025-08-07 02:17:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_021754.html
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.77 秒
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9601
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9876
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9895
2025-08-07 02:17:54 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9919
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 9.835073932081382, 'kernel': 'rbf'}
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_021755.html
2025-08-07 02:17:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_021755.html
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.39 秒
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9753
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:17:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:17:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_021755.html
2025-08-07 02:17:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:17:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_021756.html
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.80 秒
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 02:17:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 02:17:57 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9815
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (50, 50), 'alpha': 0.005733123343606008}
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9815
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-07 02:18:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 02:18:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:18:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:18:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_021809.html
2025-08-07 02:18:10 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 02:18:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 02:18:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_021810.html
2025-08-07 02:18:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 13.83 秒
2025-08-07 02:18:44 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 02:18:44 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 02:18:44 - model_training - INFO - 模型名称: Decision Tree
2025-08-07 02:18:44 - model_training - INFO - 准确率: 0.8000
2025-08-07 02:18:44 - model_training - INFO - AUC: 0.8951
2025-08-07 02:18:44 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:44 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-07 02:18:44 - model_training - INFO - 
分类报告:
2025-08-07 02:18:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-07 02:18:44 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:18:44 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-07 02:18:44 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-08-07 02:18:44 - model_training - INFO - 模型名称: Random Forest
2025-08-07 02:18:44 - model_training - INFO - 准确率: 0.9000
2025-08-07 02:18:44 - model_training - INFO - AUC: 0.9386
2025-08-07 02:18:44 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:44 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 02:18:44 - model_training - INFO - 
分类报告:
2025-08-07 02:18:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 02:18:44 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 02:18:44 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-07 02:18:44 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-07 02:18:44 - model_training - INFO - 模型名称: XGBoost
2025-08-07 02:18:44 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:18:44 - model_training - INFO - AUC: 0.9668
2025-08-07 02:18:44 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:44 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:18:44 - model_training - INFO - 
分类报告:
2025-08-07 02:18:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:18:44 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 02:18:44 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-07 02:18:44 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-07 02:18:44 - model_training - INFO - 模型名称: LightGBM
2025-08-07 02:18:44 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:18:44 - model_training - INFO - AUC: 0.9463
2025-08-07 02:18:44 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:44 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:18:44 - model_training - INFO - 
分类报告:
2025-08-07 02:18:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:18:44 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 02:18:44 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-07 02:18:44 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-07 02:18:45 - model_training - INFO - 模型名称: CatBoost
2025-08-07 02:18:45 - model_training - INFO - 准确率: 0.9000
2025-08-07 02:18:45 - model_training - INFO - AUC: 0.9591
2025-08-07 02:18:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:45 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 02:18:45 - model_training - INFO - 
分类报告:
2025-08-07 02:18:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 02:18:45 - model_training - INFO - 训练时间: 1.02 秒
2025-08-07 02:18:45 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-07 02:18:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-08-07 02:18:45 - model_training - INFO - 模型名称: Logistic Regression
2025-08-07 02:18:45 - model_training - INFO - 准确率: 0.8500
2025-08-07 02:18:45 - model_training - INFO - AUC: 0.9284
2025-08-07 02:18:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:45 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-07 02:18:45 - model_training - INFO - 
分类报告:
2025-08-07 02:18:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-07 02:18:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:18:45 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-07 02:18:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-07 02:18:45 - model_training - INFO - 模型名称: SVM
2025-08-07 02:18:45 - model_training - INFO - 准确率: 0.8000
2025-08-07 02:18:45 - model_training - INFO - AUC: 0.9207
2025-08-07 02:18:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:45 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-08-07 02:18:45 - model_training - INFO - 
分类报告:
2025-08-07 02:18:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-08-07 02:18:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:18:45 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-07 02:18:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-08-07 02:18:45 - model_training - INFO - 模型名称: KNN
2025-08-07 02:18:45 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:18:45 - model_training - INFO - AUC: 0.9322
2025-08-07 02:18:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:45 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:18:45 - model_training - INFO - 
分类报告:
2025-08-07 02:18:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:18:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:18:45 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-07 02:18:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-08-07 02:18:45 - model_training - INFO - 模型名称: Naive Bayes
2025-08-07 02:18:45 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:18:45 - model_training - INFO - AUC: 0.8977
2025-08-07 02:18:45 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:45 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-07 02:18:45 - model_training - INFO - 
分类报告:
2025-08-07 02:18:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:18:45 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 02:18:45 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-07 02:18:45 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-08-07 02:18:46 - model_training - INFO - 模型名称: Neural Network
2025-08-07 02:18:46 - model_training - INFO - 准确率: 0.8750
2025-08-07 02:18:46 - model_training - INFO - AUC: 0.9591
2025-08-07 02:18:46 - model_training - INFO - 混淆矩阵:
2025-08-07 02:18:46 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 02:18:46 - model_training - INFO - 
分类报告:
2025-08-07 02:18:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 02:18:46 - model_training - INFO - 训练时间: 0.24 秒
2025-08-07 02:18:46 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-07 02:18:46 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-08-07 02:20:24 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:24 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:24 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:24 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:24 - best_model_selector - INFO - 使用 balanced 策略计算综合得分...
2025-08-07 02:20:24 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8848)
2025-08-07 02:20:24 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:24 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:24 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:24 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:24 - best_model_selector - INFO - 推荐策略: balanced
2025-08-07 02:20:24 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:20:24 - best_model_selector - INFO - 综合得分: 0.8848
2025-08-07 02:20:24 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:24 - best_model_selector - INFO -   1. CatBoost: 0.8848
2025-08-07 02:20:24 - best_model_selector - INFO -   2. Random Forest: 0.8796
2025-08-07 02:20:24 - best_model_selector - INFO -   3. XGBoost: 0.8611
2025-08-07 02:20:24 - best_model_selector - INFO -   4. Neural Network: 0.8592
2025-08-07 02:20:24 - best_model_selector - INFO -   5. LightGBM: 0.8560
2025-08-07 02:20:24 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在平衡策略下，该模型获得了最高的综合得分 0.8848
2. 在多个关键指标上表现均衡：
   - AUC-ROC: 0.9591
   - F1分数: 0.8750
   - MCC: 0.7965

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:24 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:44 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:44 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:44 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:44 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:44 - best_model_selector - INFO - 使用 performance 策略计算综合得分...
2025-08-07 02:20:44 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.9061)
2025-08-07 02:20:44 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:44 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:44 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:44 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:44 - best_model_selector - INFO - 推荐策略: performance
2025-08-07 02:20:44 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:20:44 - best_model_selector - INFO - 综合得分: 0.9061
2025-08-07 02:20:44 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:44 - best_model_selector - INFO -   1. CatBoost: 0.9061
2025-08-07 02:20:44 - best_model_selector - INFO -   2. Random Forest: 0.9000
2025-08-07 02:20:44 - best_model_selector - INFO -   3. XGBoost: 0.8827
2025-08-07 02:20:44 - best_model_selector - INFO -   4. Neural Network: 0.8804
2025-08-07 02:20:44 - best_model_selector - INFO -   5. LightGBM: 0.8766
2025-08-07 02:20:44 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在性能优先策略下，该模型获得了最高的综合得分 0.9061
2. AUC-ROC: 0.9591，表现出色的分类能力
3. F1分数: 0.8750，在精确率和召回率之间取得良好平衡

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:44 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:48 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:48 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:48 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:48 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:48 - best_model_selector - INFO - 使用 robustness 策略计算综合得分...
2025-08-07 02:20:48 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: CatBoost (得分: 0.8471)
2025-08-07 02:20:48 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:48 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:48 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:48 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:48 - best_model_selector - INFO - 推荐策略: robustness
2025-08-07 02:20:48 - best_model_selector - INFO - 最佳模型: CatBoost
2025-08-07 02:20:48 - best_model_selector - INFO - 综合得分: 0.8471
2025-08-07 02:20:48 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:48 - best_model_selector - INFO -   1. CatBoost: 0.8471
2025-08-07 02:20:48 - best_model_selector - INFO -   2. Random Forest: 0.8450
2025-08-07 02:20:48 - best_model_selector - INFO -   3. XGBoost: 0.8124
2025-08-07 02:20:48 - best_model_selector - INFO -   4. Neural Network: 0.8117
2025-08-07 02:20:48 - best_model_selector - INFO -   5. LightGBM: 0.8104
2025-08-07 02:20:48 - best_model_selector - INFO - 
推荐 CatBoost 作为最佳模型的理由：

1. 在稳健性优先策略下，该模型获得了最高的综合得分 0.8471
2. 马修斯相关系数(MCC): 0.7965，显示出良好的稳健性
3. 平衡准确率: 0.8900，在不平衡数据上表现稳定

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:48 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:55 - best_model_selector - INFO - 开始自动选择最佳模型...
2025-08-07 02:20:55 - best_model_selector - INFO - 加载模型训练结果...
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 DecisionTree 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 RandomForest 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 XGBoost 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 LightGBM 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 CatBoost 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 Logistic 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 SVM 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 KNN 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 NaiveBayes 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 成功加载模型 NeuralNet 的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 共加载了 10 个模型的结果
2025-08-07 02:20:55 - best_model_selector - INFO - 计算综合性能指标...
2025-08-07 02:20:55 - best_model_selector - INFO - 成功计算了 10 个模型的性能指标
2025-08-07 02:20:55 - best_model_selector - INFO - 使用 interpretability 策略计算综合得分...
2025-08-07 02:20:55 - best_model_selector - INFO - 最佳模型选择完成，推荐模型: RandomForest (得分: 0.8752)
2025-08-07 02:20:55 - best_model_selector - INFO - 模型选择结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\best_model_selection.json
2025-08-07 02:20:55 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:55 - best_model_selector - INFO - 最佳模型推荐结果
2025-08-07 02:20:55 - best_model_selector - INFO - ============================================================
2025-08-07 02:20:55 - best_model_selector - INFO - 推荐策略: interpretability
2025-08-07 02:20:55 - best_model_selector - INFO - 最佳模型: Random Forest
2025-08-07 02:20:55 - best_model_selector - INFO - 综合得分: 0.8752
2025-08-07 02:20:55 - best_model_selector - INFO - 
前三名模型:
2025-08-07 02:20:55 - best_model_selector - INFO -   1. Random Forest: 0.8752
2025-08-07 02:20:55 - best_model_selector - INFO -   2. Logistic Regression: 0.8669
2025-08-07 02:20:55 - best_model_selector - INFO -   3. CatBoost: 0.8543
2025-08-07 02:20:55 - best_model_selector - INFO -   4. Decision Tree: 0.8540
2025-08-07 02:20:55 - best_model_selector - INFO -   5. XGBoost: 0.8416
2025-08-07 02:20:55 - best_model_selector - INFO - 
推荐 Random Forest 作为最佳模型的理由：

1. 在可解释性优先策略下，该模型获得了最高的综合得分 0.8752
2. 可解释性评分: 0.80，模型决策过程相对透明
3. 在保持可解释性的同时，F1分数达到 0.8750

4. 该模型在二分类任务中展现出了优秀的综合性能
2025-08-07 02:20:55 - best_model_selector - INFO - ============================================================
